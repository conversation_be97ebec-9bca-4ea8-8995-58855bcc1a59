"""
AFSIM Platform Agent - 文档处理和解析模块
专门用于处理AFSIM Platform相关的MD文档，提取参数和结构化信息
"""

import re
import json
import logging
import os
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import markdown
from bs4 import BeautifulSoup
import yaml
from openai import OpenAI

logger = logging.getLogger(__name__)


class PlatformDocumentProcessor:
    """Platform文档处理器"""
    
    def __init__(self, config_path: str = None):
        """
        初始化文档处理器

        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.platform_params = {}
        self.processed_docs = {}
        self.llm_client = None
        self._initialize_llm()
        
    def _load_config(self, config_path: str = None) -> Dict[str, Any]:
        """加载配置文件"""
        if config_path is None:
            config_path = Path(__file__).parent.parent / "config" / "config.yaml"
            
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'document_processing': {
                'docs_path': 'docs',
                'chunk_size': 500,
                'chunk_overlap': 50,
                'min_content_length': 50,
                'encoding': 'utf-8'
            },
            'llm': {
                'provider': 'deepseek',
                'deepseek': {
                    'api_key': '',
                    'base_url': 'https://api.deepseek.com/v1',
                    'model': 'deepseek-chat',
                    'temperature': 0.1,
                    'max_tokens': 4000
                }
            }
        }

    def _initialize_llm(self):
        """初始化LLM客户端"""
        try:
            llm_config = self.config.get('llm', {})
            provider = llm_config.get('provider', 'deepseek')

            if provider == 'deepseek':
                config = llm_config.get('deepseek', {})
                api_key = config.get('api_key') or os.getenv('DEEPSEEK_API_KEY')

                if not api_key:
                    logger.warning("未设置DeepSeek API Key，将使用传统正则表达式方法提取参数")
                    return

                self.llm_client = OpenAI(
                    api_key=api_key,
                    base_url=config.get('base_url', 'https://api.deepseek.com/v1')
                )
                self.model = config.get('model', 'deepseek-chat')
                self.temperature = config.get('temperature', 0.1)
                self.max_tokens = config.get('max_tokens', 4000)

                logger.info("LLM客户端初始化成功，将使用AI方法提取参数")

            else:
                logger.warning(f"不支持的LLM提供商: {provider}，将使用传统正则表达式方法")

        except Exception as e:
            logger.error(f"LLM客户端初始化失败: {e}，将使用传统正则表达式方法")
            self.llm_client = None
    
    def process_md_file(self, file_path: str) -> Dict[str, Any]:
        """
        处理单个MD文件
        
        Args:
            file_path: MD文件路径
            
        Returns:
            Dict: 处理结果
        """
        try:
            logger.info(f"开始处理文件: {file_path}")
            
            # 读取文件内容
            with open(file_path, 'r', encoding=self.config['document_processing']['encoding']) as f:
                content = f.read()
            
            # 解析文档结构
            doc_info = self._parse_document_structure(content, file_path)

            # 创建文档块
            chunks = self._create_document_chunks(content, doc_info)

            result = {
                'file_path': file_path,
                'doc_info': doc_info,
                'chunks': chunks,
                'status': 'success'
            }

            logger.info(f"文件处理完成: {file_path}, 创建了 {len(chunks)} 个文档块")
            return result
            
        except Exception as e:
            logger.error(f"处理文件 {file_path} 失败: {e}")
            return {
                'file_path': file_path,
                'error': str(e),
                'status': 'error'
            }
    
    def _parse_document_structure(self, content: str, file_path: str) -> Dict[str, Any]:
        """
        解析文档结构
        
        Args:
            content: 文档内容
            file_path: 文件路径
            
        Returns:
            Dict: 文档结构信息
        """
        # 转换为HTML
        html_content = markdown.markdown(content)
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 提取标题
        title_tag = soup.find(['h1', 'h2'])
        title = title_tag.get_text().strip() if title_tag else Path(file_path).stem
        
        # 提取章节
        sections = []
        current_section = None
        
        for element in soup.find_all(['h1', 'h2', 'h3', 'h4', 'p', 'ul', 'ol', 'pre', 'code']):
            if element.name.startswith('h'):
                # 保存上一个章节
                if current_section:
                    sections.append(current_section)
                
                # 创建新章节
                level = int(element.name[1])
                current_section = {
                    'title': element.get_text().strip(),
                    'level': level,
                    'content': '',
                    'type': 'section'
                }
            elif current_section:
                # 添加内容到当前章节
                text = element.get_text().strip()
                if text:
                    current_section['content'] += f" {text}"
        
        # 添加最后一个章节
        if current_section:
            sections.append(current_section)
        
        return {
            'title': title,
            'file_name': Path(file_path).name,
            'sections': sections,
            'total_sections': len(sections)
        }
    
    def _extract_platform_parameters(self, content: str) -> List[Dict[str, Any]]:
        """
        提取Platform相关参数

        Args:
            content: 文档内容

        Returns:
            List[Dict]: 参数列表
        """
        # 如果有LLM客户端，使用AI方法提取参数
        if self.llm_client:
            return self._extract_parameters_with_llm(content)
        else:
            # 使用传统正则表达式方法
            return self._extract_parameters_with_regex(content)

    def _extract_parameters_with_llm(self, content: str) -> List[Dict[str, Any]]:
        """
        使用大模型提取Platform参数

        Args:
            content: 文档内容

        Returns:
            List[Dict]: 参数列表
        """
        try:
            # 限制内容长度，避免超过模型限制
            max_content_length = 8000
            if len(content) > max_content_length:
                # 尝试保留包含参数信息的重要部分
                content = self._extract_parameter_sections(content, max_content_length)

            # 构建提示词
            prompt = self._build_parameter_extraction_prompt(content)

            # 调用LLM
            response = self.llm_client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "你是一个专业的AFSIM技术文档分析师，擅长从技术文档中提取Platform参数信息。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=self.temperature,
                max_tokens=self.max_tokens
            )

            # 解析LLM响应
            response_text = response.choices[0].message.content
            parameters = self._parse_llm_parameter_response(response_text)

            logger.info(f"使用AI方法提取到 {len(parameters)} 个参数")
            return parameters

        except Exception as e:
            logger.error(f"AI参数提取失败: {e}，回退到正则表达式方法")
            return self._extract_parameters_with_regex(content)

    def _extract_parameters_with_regex(self, content: str) -> List[Dict[str, Any]]:
        """
        使用正则表达式提取Platform参数（传统方法）

        Args:
            content: 文档内容

        Returns:
            List[Dict]: 参数列表
        """
        parameters = []

        # 定义Platform参数模式
        param_patterns = [
            # 基本参数模式: [parameter_name]() ...
            r'\[([a-zA-Z_][a-zA-Z0-9_]*)\]\(\)\s*\.{3}',
            # 命令模式: parameter_name ... end_parameter
            r'([a-zA-Z_][a-zA-Z0-9_]*)\s+\.{3}\s+end_\1',
            # 脚本类模式: WsfPlatform
            r'`(Wsf[A-Z][a-zA-Z]*)`',
            # 属性模式: platform.property
            r'platform\.([a-zA-Z_][a-zA-Z0-9_]*)',
        ]

        # 提取参数
        for pattern in param_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE | re.MULTILINE)
            for match in matches:
                param_name = match.group(1)

                # 获取参数上下文
                start_pos = max(0, match.start() - 200)
                end_pos = min(len(content), match.end() + 200)
                context = content[start_pos:end_pos].strip()

                # 尝试提取参数描述
                description = self._extract_parameter_description(content, match.start(), param_name)

                param_info = {
                    'name': param_name,
                    'type': self._infer_parameter_type(param_name, context),
                    'description': description,
                    'context': context,
                    'position': match.start(),
                    'default_value': None,  # 正则方法无法提取默认值
                    'extraction_method': 'regex'
                }

                # 避免重复参数
                if not any(p['name'] == param_name for p in parameters):
                    parameters.append(param_info)

        # 按位置排序
        parameters.sort(key=lambda x: x['position'])

        logger.info(f"使用正则表达式方法提取到 {len(parameters)} 个参数")
        return parameters

    def _extract_parameter_sections(self, content: str, max_length: int) -> str:
        """
        从长文档中提取包含参数信息的重要部分

        Args:
            content: 原始文档内容
            max_length: 最大长度

        Returns:
            str: 提取的重要部分
        """
        # 查找包含参数关键词的段落
        parameter_keywords = [
            'parameter', 'param', 'argument', 'option', 'setting', 'config',
            'platform', 'wsf', 'command', 'property', 'attribute',
            'default', 'value', 'type', 'description'
        ]

        lines = content.split('\n')
        important_lines = []

        for i, line in enumerate(lines):
            line_lower = line.lower()
            # 如果行包含参数关键词，包含该行及其上下文
            if any(keyword in line_lower for keyword in parameter_keywords):
                # 添加上下文（前后各2行）
                start_idx = max(0, i - 2)
                end_idx = min(len(lines), i + 3)
                for j in range(start_idx, end_idx):
                    if lines[j] not in important_lines:
                        important_lines.append(lines[j])

        # 如果提取的内容仍然太长，截取前面部分
        extracted_content = '\n'.join(important_lines)
        if len(extracted_content) > max_length:
            extracted_content = extracted_content[:max_length] + "..."

        # 如果提取的内容太少，使用原文档的前面部分
        if len(extracted_content) < max_length // 2:
            extracted_content = content[:max_length] + "..."

        return extracted_content

    def _build_parameter_extraction_prompt(self, content: str) -> str:
        """
        构建参数提取的提示词

        Args:
            content: 文档内容

        Returns:
            str: 提示词
        """
        return f"""
请从以下AFSIM Platform技术文档中提取所有的参数信息。

文档内容：
{content}

请提取以下信息并以JSON格式返回：
1. 参数名称 (name)
2. 参数类型 (type): 如 string, number, boolean, coordinate, angle, distance, time 等
3. 参数描述 (description): 参数的作用和用途
4. 默认值 (default_value): 如果文档中提到了默认值
5. 是否必需 (required): true/false
6. 示例值 (example): 如果文档中有示例

返回格式：
```json
[
  {{
    "name": "参数名称",
    "type": "参数类型",
    "description": "参数描述",
    "default_value": "默认值或null",
    "required": true/false,
    "example": "示例值或null"
  }}
]
```

注意事项：
1. 只提取明确的Platform参数，忽略一般性的文档说明
2. 参数名称应该是实际在代码中使用的名称
3. 描述要简洁明了，重点说明参数的作用
4. 如果无法确定某个字段的值，请设置为null
5. 确保返回的是有效的JSON格式
"""

    def _parse_llm_parameter_response(self, response_text: str) -> List[Dict[str, Any]]:
        """
        解析LLM返回的参数信息

        Args:
            response_text: LLM响应文本

        Returns:
            List[Dict]: 解析后的参数列表
        """
        try:
            # 尝试提取JSON部分
            json_start = response_text.find('[')
            json_end = response_text.rfind(']') + 1

            if json_start != -1 and json_end > json_start:
                json_text = response_text[json_start:json_end]
                parameters = json.loads(json_text)

                # 标准化参数格式
                standardized_params = []
                for param in parameters:
                    if isinstance(param, dict) and 'name' in param:
                        standardized_param = {
                            'name': param.get('name', ''),
                            'type': param.get('type', 'unknown'),
                            'description': param.get('description', ''),
                            'default_value': param.get('default_value'),
                            'required': param.get('required', False),
                            'example': param.get('example'),
                            'extraction_method': 'llm',
                            'position': len(standardized_params)  # 使用索引作为位置
                        }
                        standardized_params.append(standardized_param)

                return standardized_params
            else:
                logger.warning("LLM响应中未找到有效的JSON格式")
                return []

        except json.JSONDecodeError as e:
            logger.error(f"解析LLM响应JSON失败: {e}")
            logger.debug(f"响应内容: {response_text}")
            return []
        except Exception as e:
            logger.error(f"解析LLM响应失败: {e}")
            return []
    
    def _extract_parameter_description(self, content: str, position: int, param_name: str) -> str:
        """
        提取参数描述
        
        Args:
            content: 文档内容
            position: 参数位置
            param_name: 参数名称
            
        Returns:
            str: 参数描述
        """
        # 在参数前后查找描述
        lines = content.split('\n')
        param_line = None
        
        # 找到参数所在行
        for i, line in enumerate(lines):
            if param_name in line and abs(sum(len(l) for l in lines[:i]) - position) < 100:
                param_line = i
                break
        
        if param_line is None:
            return ""
        
        # 查找描述（通常在参数后面的几行）
        description_lines = []
        for i in range(param_line + 1, min(param_line + 5, len(lines))):
            line = lines[i].strip()
            if line and not line.startswith('[') and not line.startswith('#'):
                description_lines.append(line)
            elif description_lines:  # 遇到空行或新参数，停止
                break
        
        return ' '.join(description_lines)
    
    def _infer_parameter_type(self, param_name: str, context: str) -> str:
        """
        推断参数类型
        
        Args:
            param_name: 参数名称
            context: 参数上下文
            
        Returns:
            str: 参数类型
        """
        # 基于参数名称推断类型
        if any(keyword in param_name.lower() for keyword in ['position', 'coordinate', 'point']):
            return 'coordinate'
        elif any(keyword in param_name.lower() for keyword in ['heading', 'angle', 'rotation']):
            return 'angle'
        elif any(keyword in param_name.lower() for keyword in ['altitude', 'height', 'depth']):
            return 'distance'
        elif any(keyword in param_name.lower() for keyword in ['mass', 'weight']):
            return 'mass'
        elif any(keyword in param_name.lower() for keyword in ['time', 'duration']):
            return 'time'
        elif any(keyword in param_name.lower() for keyword in ['name', 'id', 'type']):
            return 'string'
        elif any(keyword in param_name.lower() for keyword in ['factor', 'ratio', 'percentage']):
            return 'number'
        elif any(keyword in param_name.lower() for keyword in ['enable', 'disable', 'flag']):
            return 'boolean'
        else:
            return 'unknown'
    
    def _create_document_chunks(self, content: str, doc_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        创建文档块
        
        Args:
            content: 文档内容
            doc_info: 文档信息
            
        Returns:
            List[Dict]: 文档块列表
        """
        chunks = []
        chunk_size = self.config.get('document_processing', {}).get('chunk_size', 500)
        chunk_overlap = self.config.get('document_processing', {}).get('chunk_overlap', 50)
        min_length = self.config.get('document_processing', {}).get('min_content_length', 50)
        
        # 清理内容
        cleaned_content = self._clean_content(content)
        
        # 按章节分块
        for section in doc_info['sections']:
            section_content = section['content'].strip()
            
            if len(section_content) < min_length:
                continue
            
            # 如果章节内容较短，直接作为一个块
            if len(section_content) <= chunk_size:
                chunks.append({
                    'content': section_content,
                    'metadata': {
                        'section_title': section['title'],
                        'section_level': section['level'],
                        'doc_title': doc_info['title'],
                        'chunk_type': 'section',
                        'chunk_index': len(chunks)
                    }
                })
            else:
                # 分割长章节
                for i in range(0, len(section_content), chunk_size - chunk_overlap):
                    chunk_content = section_content[i:i + chunk_size]
                    
                    if len(chunk_content.strip()) >= min_length:
                        chunks.append({
                            'content': chunk_content,
                            'metadata': {
                                'section_title': section['title'],
                                'section_level': section['level'],
                                'doc_title': doc_info['title'],
                                'chunk_type': 'section_part',
                                'chunk_index': len(chunks),
                                'part_index': i // (chunk_size - chunk_overlap)
                            }
                        })
        
        return chunks
    
    def _clean_content(self, content: str) -> str:
        """
        清理文档内容
        
        Args:
            content: 原始内容
            
        Returns:
            str: 清理后的内容
        """
        # 移除Markdown格式
        content = re.sub(r'!\[.*?\]\(.*?\)', '', content)  # 图片
        content = re.sub(r'\[([^\]]+)\]\([^\)]+\)', r'\1', content)  # 链接
        content = re.sub(r'`([^`]+)`', r'\1', content)  # 行内代码
        content = re.sub(r'\*\*(.*?)\*\*', r'\1', content)  # 粗体
        content = re.sub(r'\*(.*?)\*', r'\1', content)  # 斜体
        
        # 清理多余空白
        content = re.sub(r'\n\s*\n', '\n\n', content)  # 多个空行
        content = re.sub(r' +', ' ', content)  # 多个空格
        
        return content.strip()
    
    def process_directory(self, docs_path: str = None) -> Dict[str, Any]:
        """
        处理整个文档目录
        
        Args:
            docs_path: 文档目录路径
            
        Returns:
            Dict: 处理结果统计
        """
        if docs_path is None:
            docs_path = self.config['document_processing']['docs_path']
        
        docs_path = Path(docs_path)
        if not docs_path.exists():
            logger.error(f"文档目录不存在: {docs_path}")
            return {'error': f'文档目录不存在: {docs_path}'}
        
        # 查找所有MD文件
        md_files = list(docs_path.rglob('*.md')) + list(docs_path.rglob('*.markdown'))
        
        logger.info(f"找到 {len(md_files)} 个Markdown文件")
        
        results = {
            'total_files': len(md_files),
            'processed_files': 0,
            'failed_files': 0,
            'total_chunks': 0,
            'files': []
        }
        
        for md_file in md_files:
            try:
                result = self.process_md_file(str(md_file))
                results['files'].append(result)
                
                if result['status'] == 'success':
                    results['processed_files'] += 1
                    results['total_chunks'] += len(result['chunks'])
                else:
                    results['failed_files'] += 1
                    
            except Exception as e:
                logger.error(f"处理文件 {md_file} 时发生错误: {e}")
                results['failed_files'] += 1
        
        logger.info(f"目录处理完成: 成功 {results['processed_files']}, 失败 {results['failed_files']}")
        return results
