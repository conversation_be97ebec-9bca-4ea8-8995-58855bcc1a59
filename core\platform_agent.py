"""
AFSIM Platform Agent - 核心Agent类
整合所有模块，提供platform脚本生成和参数查询功能
"""

import os
import logging
import json
from pathlib import Path
from typing import List, Dict, Any, Optional
import yaml

from .vector_database import VectorDatabase
from .document_processor import PlatformDocumentProcessor
from .chinese_summarizer import ChineseSummarizer
from .parameter_extractor import ParameterExtractor

logger = logging.getLogger(__name__)


class AFSIMPlatformAgent:
    """AFSIM Platform Agent主类"""
    
    def __init__(self, config_path: str = None):
        """
        初始化Agent
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config = self._load_config(config_path)
        
        # 初始化组件
        self.vector_db = VectorDatabase(config_path)
        self.doc_processor = PlatformDocumentProcessor(config_path)
        self.summarizer = ChineseSummarizer(config_path)
        self.parameter_extractor = ParameterExtractor(config_path)
        
        # 状态标志
        self.is_initialized = False
        self.indexed_files = set()
        
        # 设置日志
        self._setup_logging()
        
    def _load_config(self, config_path: str = None) -> Dict[str, Any]:
        """加载配置文件"""
        if config_path is None:
            config_path = Path(__file__).parent.parent / "config" / "config.yaml"
            
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return {}
    
    def _setup_logging(self):
        """设置日志"""
        log_config = self.config.get('logging', {})
        log_level = log_config.get('level', 'INFO')
        log_format = log_config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        
        logging.basicConfig(
            level=getattr(logging, log_level.upper(), logging.INFO),
            format=log_format
        )
    
    def initialize(self) -> bool:
        """
        初始化Agent
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            logger.info("开始初始化AFSIM Platform Agent...")
            
            # 初始化向量数据库
            if not self.vector_db.initialize():
                logger.error("向量数据库初始化失败")
                return False
            
            logger.info("AFSIM Platform Agent初始化完成")
            self.is_initialized = True
            return True
            
        except Exception as e:
            logger.error(f"Agent初始化失败: {e}")
            return False
    
    def index_documents(self, docs_path: str = None, force_reindex: bool = False) -> Dict[str, Any]:
        """
        索引文档到向量数据库
        
        Args:
            docs_path: 文档路径
            force_reindex: 是否强制重新索引
            
        Returns:
            Dict: 索引结果
        """
        try:
            if not self.is_initialized:
                logger.error("Agent未初始化")
                return {"error": "Agent未初始化"}
            
            logger.info("开始索引文档...")
            
            # 处理文档
            process_result = self.doc_processor.process_directory(docs_path)
            
            if 'error' in process_result:
                return process_result
            
            # 准备向量数据库数据
            documents = []
            metadatas = []
            ids = []
            
            for file_result in process_result['files']:
                if file_result['status'] != 'success':
                    continue
                
                file_path = file_result['file_path']
                doc_info = file_result['doc_info']
                
                # 添加文档块到向量数据库
                for chunk in file_result['chunks']:
                    content = chunk['content']
                    metadata = chunk['metadata']
                    
                    # 增强元数据
                    metadata.update({
                        'file_path': file_path,
                        'file_name': doc_info['file_name'],
                        'source_type': 'platform_doc'
                    })
                    
                    documents.append(content)
                    metadatas.append(metadata)
                    ids.append(f"{Path(file_path).stem}_{metadata['chunk_index']}")
            
            # 添加到向量数据库
            if documents:
                success = self.vector_db.add_documents(documents, metadatas, ids)
                if success:
                    logger.info(f"成功索引 {len(documents)} 个文档块")
                else:
                    logger.error("向量数据库索引失败")
                    return {"error": "向量数据库索引失败"}
            
            # 更新索引状态
            for file_result in process_result['files']:
                if file_result['status'] == 'success':
                    self.indexed_files.add(file_result['file_path'])
            
            result = {
                "status": "success",
                "indexed_files": len(self.indexed_files),
                "total_chunks": len(documents),
                "total_parameters": process_result['total_parameters'],
                "process_summary": process_result
            }
            
            logger.info("文档索引完成")
            return result
            
        except Exception as e:
            logger.error(f"文档索引失败: {e}")
            return {"error": str(e)}
    
    def query_platform_parameters(self, query: str, top_k: int = 5) -> Dict[str, Any]:
        """
        查询Platform参数
        
        Args:
            query: 查询文本
            top_k: 返回结果数量
            
        Returns:
            Dict: 查询结果
        """
        try:
            if not self.is_initialized:
                return {"error": "Agent未初始化"}
            
            logger.info(f"查询Platform参数: {query}")
            
            # 向量搜索
            search_results = self.vector_db.search(
                query=query,
                top_k=top_k,
                filter_metadata={"source_type": "platform_doc"}
            )
            
            if not search_results:
                return {
                    "query": query,
                    "results": [],
                    "summary": "未找到相关的Platform参数信息"
                }
            
            # 提取相关内容
            relevant_content = []
            for result in search_results:
                relevant_content.append({
                    "content": result['content'],
                    "metadata": result['metadata'],
                    "similarity": 1 - result['distance']  # 转换为相似度
                })
            
            # 生成中文总结
            combined_content = "\n\n".join([r['content'] for r in relevant_content])
            summary_result = self.summarizer.summarize_document_content(
                combined_content, 
                f"Platform参数查询: {query}"
            )
            
            result = {
                "query": query,
                "results": relevant_content,
                "summary": summary_result.get('summary', ''),
                "total_results": len(search_results)
            }
            
            logger.info(f"查询完成，返回 {len(search_results)} 个结果")
            return result
            
        except Exception as e:
            logger.error(f"查询Platform参数失败: {e}")
            return {"error": str(e)}
    
    def generate_platform_script(self, platform_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成Platform脚本
        
        Args:
            platform_config: Platform配置
            
        Returns:
            Dict: 生成结果
        """
        try:
            logger.info("开始生成Platform脚本...")
            
            # 验证必需参数
            required_params = self.config.get('platform_generation', {}).get('validation', {}).get('required_params', [])
            for param in required_params:
                if param not in platform_config:
                    return {"error": f"缺少必需参数: {param}"}
            
            # 获取默认参数
            default_params = self.config.get('platform_generation', {}).get('default_params', {})
            
            # 合并配置
            final_config = {**default_params, **platform_config}
            
            # 生成脚本内容
            script_content = self._build_platform_script(final_config)
            
            # 生成中文说明
            explanation = self._generate_script_explanation(final_config)
            
            result = {
                "status": "success",
                "script_content": script_content,
                "explanation": explanation,
                "config_used": final_config
            }
            
            logger.info("Platform脚本生成完成")
            return result
            
        except Exception as e:
            logger.error(f"生成Platform脚本失败: {e}")
            return {"error": str(e)}
    
    def _build_platform_script(self, config: Dict[str, Any]) -> str:
        """构建Platform脚本"""
        platform_name = config.get('platform_name', 'my_platform')
        platform_type = config.get('platform_type', 'WSF_PLATFORM')
        
        script_lines = [
            f"platform {platform_name} {platform_type}",
            ""
        ]
        
        # 添加基本参数
        if 'side' in config:
            script_lines.append(f"    side {config['side']}")
        
        if 'position' in config:
            pos = config['position']
            if isinstance(pos, dict):
                script_lines.append(f"    position {pos.get('lat', 0)} {pos.get('lon', 0)}")
            elif isinstance(pos, (list, tuple)) and len(pos) >= 2:
                script_lines.append(f"    position {pos[0]} {pos[1]}")
        
        if 'altitude' in config:
            script_lines.append(f"    altitude {config['altitude']}")
        
        if 'heading' in config:
            script_lines.append(f"    heading {config['heading']}")
        
        if 'spatial_domain' in config:
            script_lines.append(f"    spatial_domain {config['spatial_domain']}")
        
        # 添加其他参数
        for key, value in config.items():
            if key not in ['platform_name', 'platform_type', 'side', 'position', 'altitude', 'heading', 'spatial_domain']:
                script_lines.append(f"    {key} {value}")
        
        script_lines.append("")
        script_lines.append("end_platform")
        
        return "\n".join(script_lines)
    
    def _generate_script_explanation(self, config: Dict[str, Any]) -> str:
        """生成脚本说明"""
        try:
            if not self.summarizer.client:
                return "无法生成脚本说明（LLM未配置）"
            
            config_text = json.dumps(config, indent=2, ensure_ascii=False)
            
            prompt = f"""请为以下AFSIM Platform配置生成中文说明：

配置参数：
{config_text}

请提供：
1. 配置概述
2. 各参数的作用说明
3. 使用场景
4. 注意事项

请使用简洁明了的中文。
"""
            
            response = self.summarizer.client.chat.completions.create(
                model=self.summarizer.model,
                messages=[
                    {"role": "system", "content": "你是一个AFSIM专家，擅长解释Platform配置。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=1000
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"生成脚本说明失败: {e}")
            return "脚本说明生成失败"
    
    def get_all_parameters(self, parameter_type: str = "platform") -> Dict[str, Any]:
        """
        获取所有已索引的参数

        Args:
            parameter_type: 参数类型 (platform, sensor, weapon等)

        Returns:
            Dict: 参数信息
        """
        try:
            if not self.is_initialized:
                return {"error": "Agent未初始化"}

            # 搜索相关内容
            search_query = f"{parameter_type} parameters commands"
            search_results = self.vector_db.search(
                query=search_query,
                top_k=50,
                filter_metadata={"source_type": "platform_doc"}
            )

            if not search_results:
                return {
                    "total_parameters": 0,
                    "parameters": [],
                    "summary": f"未找到{parameter_type}相关参数",
                    "indexed_files": len(self.indexed_files)
                }

            # 提取参数信息
            all_content = "\n".join([r['content'] for r in search_results])

            # 使用参数提取器提取参数
            parameters = self.parameter_extractor.extract_parameters(all_content, parameter_type)

            # 生成参数总结
            summary_result = self.summarizer.summarize_platform_parameters(parameters)

            result = {
                "total_parameters": len(parameters),
                "parameters": parameters,
                "summary": summary_result,
                "indexed_files": len(self.indexed_files),
                "parameter_type": parameter_type
            }

            logger.info(f"获取到 {len(parameters)} 个{parameter_type}参数")
            return result

        except Exception as e:
            logger.error(f"获取参数失败: {e}")
            return {"error": str(e)}

    def extract_parameters_from_query(self, query: str, parameter_type: str = "platform", top_k: int = 10) -> Dict[str, Any]:
        """
        根据查询从向量库中提取相关参数

        Args:
            query: 查询内容
            parameter_type: 参数类型
            top_k: 返回结果数量

        Returns:
            Dict: 提取结果
        """
        try:
            if not self.is_initialized:
                return {"error": "Agent未初始化"}

            logger.info(f"根据查询提取{parameter_type}参数: {query}")

            # 向量搜索
            search_results = self.vector_db.search(
                query=query,
                top_k=top_k,
                filter_metadata={"source_type": "platform_doc"}
            )

            if not search_results:
                return {
                    "query": query,
                    "parameter_type": parameter_type,
                    "parameters": [],
                    "summary": "未找到相关参数信息"
                }

            # 合并相关内容
            relevant_content = "\n\n".join([r['content'] for r in search_results])

            # 提取参数
            parameters = self.parameter_extractor.extract_parameters(relevant_content, parameter_type)

            # 生成总结
            if parameters:
                summary_result = self.summarizer.summarize_platform_parameters(parameters)
            else:
                summary_result = {"summary": "未从查询结果中提取到参数"}

            result = {
                "query": query,
                "parameter_type": parameter_type,
                "total_parameters": len(parameters),
                "parameters": parameters,
                "summary": summary_result,
                "search_results_count": len(search_results)
            }

            logger.info(f"查询提取完成: 找到 {len(parameters)} 个{parameter_type}参数")
            return result

        except Exception as e:
            logger.error(f"查询参数提取失败: {e}")
            return {"error": str(e)}
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取Agent状态
        
        Returns:
            Dict: 状态信息
        """
        try:
            vector_db_info = self.vector_db.get_collection_info() if self.is_initialized else {}
            
            return {
                "initialized": self.is_initialized,
                "indexed_files": len(self.indexed_files),
                "vector_db_info": vector_db_info,
                "config_loaded": bool(self.config)
            }
            
        except Exception as e:
            logger.error(f"获取状态失败: {e}")
            return {"error": str(e)}
