#!/usr/bin/env python3
"""
调试集合信息获取问题
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.vector_database import VectorDatabase

def debug_collection_info():
    """调试集合信息获取"""
    
    print("=== 调试集合信息获取 ===")
    
    # 初始化向量数据库
    vector_db = VectorDatabase()
    
    if not vector_db.initialize():
        print("❌ 向量数据库初始化失败")
        return
    
    print("✅ 向量数据库初始化成功")
    
    # 清空现有数据
    vector_db.clear_collection()
    
    # 添加测试文档
    test_documents = ["Test document 1", "Test document 2"]
    test_metadatas = [{"test": "meta1"}, {"test": "meta2"}]
    
    success = vector_db.add_documents(test_documents, test_metadatas)
    print(f"添加文档结果: {success}")
    
    # 直接调用collection.get()
    try:
        result = vector_db.collection.get()
        print(f"collection.get() 结果: {result}")
        print(f"IDs数量: {len(result['ids'])}")
        
        # 手动构建信息
        info = {
            "total_documents": len(result['ids']),
            "collection_name": vector_db.collection_name,
            "sample_ids": result['ids'][:5] if result['ids'] else []
        }
        print(f"手动构建的信息: {info}")
        
    except Exception as e:
        print(f"直接调用出错: {e}")
        import traceback
        traceback.print_exc()
    
    # 调用get_collection_info方法
    try:
        info = vector_db.get_collection_info()
        print(f"get_collection_info() 结果: {info}")
        print(f"结果类型: {type(info)}")
        print(f"结果键: {list(info.keys()) if isinstance(info, dict) else 'Not a dict'}")
        
    except Exception as e:
        print(f"get_collection_info() 出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_collection_info()
