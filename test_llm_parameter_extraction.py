#!/usr/bin/env python3
"""
测试基于LLM的参数提取功能
"""

import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.document_processor import PlatformDocumentProcessor

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_llm_parameter_extraction():
    """测试LLM参数提取功能"""
    
    # 创建测试文档内容
    test_content = """
# Platform Configuration

## Basic Parameters

### position()
Sets the initial position of the platform in geographic coordinates.

**Type**: Coordinate
**Default**: (0.0, 0.0, 0.0)
**Required**: Yes
**Example**: position(39.9042, 116.4074, 1000.0)

The position parameter defines the latitude, longitude, and altitude of the platform.

### heading()
Sets the initial heading of the platform in degrees.

**Type**: <PERSON>le (degrees)
**Default**: 0.0
**Required**: No
**Example**: heading(90.0)

### altitude()
Sets the altitude of the platform in meters above sea level.

**Type**: Distance (meters)
**Default**: 0.0
**Required**: No
**Example**: altitude(10000.0)

### platform_name()
Sets the name identifier for the platform.

**Type**: String
**Default**: "unnamed_platform"
**Required**: Yes
**Example**: platform_name("fighter_01")

### enable_sensors()
Enables or disables sensor systems on the platform.

**Type**: Boolean
**Default**: true
**Required**: No
**Example**: enable_sensors(false)

## Advanced Parameters

### max_speed()
Maximum speed of the platform in meters per second.

**Type**: Number
**Default**: 100.0
**Example**: max_speed(250.0)

### fuel_capacity()
Fuel capacity in liters.

**Type**: Number
**Default**: 1000.0
**Example**: fuel_capacity(5000.0)
"""
    
    print("=== 测试LLM参数提取功能 ===\n")
    
    # 初始化文档处理器
    processor = PlatformDocumentProcessor()
    
    # 检查LLM客户端状态
    if processor.llm_client:
        print("✅ LLM客户端已初始化，将使用AI方法提取参数")
    else:
        print("⚠️  LLM客户端未初始化，将使用传统正则表达式方法")
        print("   请设置环境变量 DEEPSEEK_API_KEY 来启用AI功能")
    
    print("\n--- 开始提取参数 ---")
    
    # 提取参数
    parameters = processor._extract_platform_parameters(test_content)
    
    print(f"\n--- 提取结果 ---")
    print(f"共提取到 {len(parameters)} 个参数：\n")
    
    # 显示提取的参数
    for i, param in enumerate(parameters, 1):
        print(f"{i}. 参数名称: {param['name']}")
        print(f"   类型: {param['type']}")
        print(f"   描述: {param['description']}")
        
        if 'default_value' in param and param['default_value']:
            print(f"   默认值: {param['default_value']}")
        
        if 'required' in param:
            print(f"   是否必需: {'是' if param['required'] else '否'}")
        
        if 'example' in param and param['example']:
            print(f"   示例: {param['example']}")
        
        print(f"   提取方法: {param.get('extraction_method', 'unknown')}")
        print()
    
    # 比较不同方法的结果
    print("--- 方法对比 ---")
    
    # 强制使用正则表达式方法
    regex_params = processor._extract_parameters_with_regex(test_content)
    print(f"正则表达式方法提取到: {len(regex_params)} 个参数")
    
    if processor.llm_client:
        # 强制使用LLM方法
        llm_params = processor._extract_parameters_with_llm(test_content)
        print(f"LLM方法提取到: {len(llm_params)} 个参数")
        
        # 分析差异
        regex_names = {p['name'] for p in regex_params}
        llm_names = {p['name'] for p in llm_params}
        
        only_regex = regex_names - llm_names
        only_llm = llm_names - regex_names
        common = regex_names & llm_names
        
        print(f"\n共同提取到的参数: {len(common)} 个")
        if common:
            print(f"  {', '.join(sorted(common))}")
        
        if only_regex:
            print(f"\n仅正则表达式提取到: {len(only_regex)} 个")
            print(f"  {', '.join(sorted(only_regex))}")
        
        if only_llm:
            print(f"\n仅LLM提取到: {len(only_llm)} 个")
            print(f"  {', '.join(sorted(only_llm))}")
    
    print("\n=== 测试完成 ===")

def test_with_real_document():
    """测试真实文档的参数提取"""
    
    print("\n=== 测试真实文档参数提取 ===")
    
    # 查找docs目录中的MD文件
    docs_path = Path("docs")
    if not docs_path.exists():
        print("❌ docs目录不存在，跳过真实文档测试")
        return
    
    md_files = list(docs_path.glob("*.md"))
    if not md_files:
        print("❌ docs目录中没有找到MD文件，跳过真实文档测试")
        return
    
    # 选择第一个MD文件进行测试
    test_file = md_files[0]
    print(f"📄 测试文件: {test_file}")
    
    try:
        with open(test_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        processor = PlatformDocumentProcessor()
        parameters = processor._extract_platform_parameters(content)
        
        print(f"✅ 从真实文档中提取到 {len(parameters)} 个参数")
        
        # 显示前5个参数
        for i, param in enumerate(parameters[:5], 1):
            print(f"\n{i}. {param['name']}")
            print(f"   类型: {param['type']}")
            print(f"   描述: {param['description'][:100]}...")
            if 'extraction_method' in param:
                print(f"   提取方法: {param['extraction_method']}")
        
        if len(parameters) > 5:
            print(f"\n... 还有 {len(parameters) - 5} 个参数")
    
    except Exception as e:
        print(f"❌ 测试真实文档时出错: {e}")

if __name__ == "__main__":
    # 检查环境变量
    api_key = os.getenv('DEEPSEEK_API_KEY')
    if not api_key:
        print("⚠️  环境变量 DEEPSEEK_API_KEY 未设置")
        print("   如需测试AI功能，请设置该环境变量")
        print("   当前将使用传统正则表达式方法进行测试\n")
    
    # 运行测试
    test_llm_parameter_extraction()
    test_with_real_document()
