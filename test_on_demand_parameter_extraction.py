#!/usr/bin/env python3
"""
测试按需参数提取功能
验证新的参数提取架构：文档索引时不提取参数，查询时按需提取
"""

import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.platform_agent import AFSIMPlatformAgent
from core.parameter_extractor import ParameterExtractor

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_parameter_extractor():
    """测试独立的参数提取器"""
    
    print("=== 测试独立参数提取器 ===\n")
    
    # 创建测试内容
    test_content = """
# Platform Configuration Guide

## Basic Platform Parameters

### position()
Sets the initial position of the platform in geographic coordinates.

**Type**: Coordinate (lat, lon, alt)
**Default**: (0.0, 0.0, 0.0)
**Required**: Yes
**Example**: position(39.9042, 116.4074, 1000.0)
**Unit**: degrees, meters

The position parameter defines the latitude, longitude, and altitude of the platform.

### heading()
Sets the initial heading of the platform.

**Type**: Angle
**Default**: 0.0
**Required**: No
**Example**: heading(90.0)
**Unit**: degrees
**Range**: 0.0 to 360.0

### max_speed()
Maximum speed of the platform.

**Type**: Number
**Default**: 100.0
**Example**: max_speed(250.0)
**Unit**: m/s
**Range**: 0.0 to 1000.0

## Sensor Parameters

### sensor_range()
Detection range of the sensor.

**Type**: Distance
**Default**: 10000.0
**Example**: sensor_range(50000.0)
**Unit**: meters

### sensor_frequency()
Operating frequency of the sensor.

**Type**: Frequency
**Default**: 10.0
**Example**: sensor_frequency(15.5)
**Unit**: GHz
"""
    
    # 初始化参数提取器
    extractor = ParameterExtractor()
    
    # 测试不同类型的参数提取
    test_cases = [
        ("platform", "平台参数"),
        ("sensor", "传感器参数"),
        ("weapon", "武器参数")
    ]
    
    for param_type, description in test_cases:
        print(f"--- 提取{description} ---")
        
        parameters = extractor.extract_parameters(test_content, param_type)
        
        print(f"提取到 {len(parameters)} 个{description}：\n")
        
        for i, param in enumerate(parameters, 1):
            print(f"{i}. {param['name']}")
            print(f"   类型: {param['type']}")
            print(f"   描述: {param['description']}")
            
            if param.get('default_value'):
                print(f"   默认值: {param['default_value']}")
            if param.get('unit'):
                print(f"   单位: {param['unit']}")
            if param.get('range'):
                print(f"   范围: {param['range']}")
            if param.get('example'):
                print(f"   示例: {param['example']}")
            
            print(f"   提取方法: {param.get('extraction_method', 'unknown')}")
            print()
        
        print("-" * 50)

def test_agent_on_demand_extraction():
    """测试Agent的按需参数提取功能"""
    
    print("\n=== 测试Agent按需参数提取 ===\n")
    
    # 初始化Agent
    agent = AFSIMPlatformAgent()
    
    # 检查初始化状态
    if not agent.initialize():
        print("❌ Agent初始化失败")
        return
    
    print("✅ Agent初始化成功")
    
    # 检查是否有文档可以测试
    docs_path = Path("docs")
    if docs_path.exists() and list(docs_path.glob("*.md")):
        print("📚 发现文档，开始索引...")
        
        # 索引文档（不提取参数）
        index_result = agent.index_documents("docs/")
        
        if index_result.get('status') == 'success':
            print(f"✅ 文档索引完成: {index_result.get('total_chunks', 0)} 个文档块")
            
            # 测试按需参数提取
            test_queries = [
                ("position altitude heading", "platform"),
                ("sensor range frequency", "sensor"),
                ("weapon damage range", "weapon"),
                ("configuration parameters", "platform")
            ]
            
            for query, param_type in test_queries:
                print(f"\n--- 查询: '{query}' (类型: {param_type}) ---")
                
                result = agent.extract_parameters_from_query(query, param_type, top_k=5)
                
                if 'error' in result:
                    print(f"❌ 查询失败: {result['error']}")
                    continue
                
                parameters = result.get('parameters', [])
                print(f"✅ 提取到 {len(parameters)} 个参数")
                
                # 显示前3个参数
                for i, param in enumerate(parameters[:3], 1):
                    print(f"\n{i}. {param['name']}")
                    print(f"   类型: {param['type']}")
                    print(f"   描述: {param['description'][:80]}...")
                    if param.get('extraction_method'):
                        print(f"   提取方法: {param['extraction_method']}")
                
                if len(parameters) > 3:
                    print(f"\n... 还有 {len(parameters) - 3} 个参数")
                
                print("-" * 40)
        else:
            print(f"❌ 文档索引失败: {index_result.get('error', '未知错误')}")
    else:
        print("⚠️  未找到docs目录或MD文件，跳过Agent测试")

def test_comparison_with_old_method():
    """对比新旧方法的差异"""
    
    print("\n=== 新旧方法对比 ===\n")
    
    # 创建测试文档
    test_doc = """
# Platform Configuration

## Basic Parameters

### [position]() ...
Sets the platform position.
Default: (0, 0, 0)
Example: position(39.9, 116.4, 1000)

### platform.heading
Platform heading in degrees.
Default: 0.0

### `WsfPlatform`
Main platform class.

## Advanced Settings

### max_velocity()
Maximum velocity of the platform.
Type: Number
Default: 100.0
Unit: m/s
Range: 0-1000
"""
    
    # 使用新的参数提取器
    extractor = ParameterExtractor()
    new_params = extractor.extract_parameters(test_doc, "platform")
    
    print(f"新方法提取到: {len(new_params)} 个参数")
    for param in new_params:
        print(f"  - {param['name']} ({param['type']}) - {param.get('extraction_method', 'unknown')}")
    
    # 如果有旧方法的代码，可以在这里对比
    # 注意：旧方法现在已经被移除，这里只是展示框架
    
    print("\n对比总结:")
    print("✅ 新方法支持按需提取，不在索引时处理")
    print("✅ 新方法支持多种参数类型")
    print("✅ 新方法提供更丰富的参数信息")
    print("✅ 新方法支持LLM和正则表达式两种模式")

def main():
    """主测试函数"""
    
    # 检查环境
    api_key = os.getenv('DEEPSEEK_API_KEY')
    if api_key:
        print("✅ 检测到DEEPSEEK_API_KEY，将测试LLM功能")
    else:
        print("⚠️  未设置DEEPSEEK_API_KEY，将使用正则表达式方法")
    
    print("=" * 60)
    
    # 运行测试
    try:
        test_parameter_extractor()
        test_agent_on_demand_extraction()
        test_comparison_with_old_method()
        
        print("\n" + "=" * 60)
        print("🎉 所有测试完成！")
        print("\n主要改进:")
        print("1. 文档索引时不再提取参数，提高索引速度")
        print("2. 支持按需提取不同类型的参数")
        print("3. 提供更丰富的参数信息（默认值、单位、范围等）")
        print("4. 支持智能LLM提取和传统正则表达式提取")
        print("5. 更好的通用性，不局限于Platform参数")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
