#!/usr/bin/env python3
"""
简化的参数提取功能测试
验证按需参数提取的核心功能
"""

import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.parameter_extractor import ParameterExtractor

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_parameter_extractor_basic():
    """测试基本的参数提取功能"""
    
    print("=== 测试基本参数提取功能 ===\n")
    
    # 创建测试内容
    test_content = """
# Platform Configuration

## Basic Parameters

### position()
Sets the initial position of the platform in geographic coordinates.

**Type**: Coordinate (lat, lon, alt)
**Default**: (0.0, 0.0, 0.0)
**Required**: Yes
**Example**: position(39.9042, 116.4074, 1000.0)
**Unit**: degrees, meters

### heading()
Sets the initial heading of the platform.

**Type**: Angle
**Default**: 0.0
**Required**: No
**Example**: heading(90.0)
**Unit**: degrees

### max_speed()
Maximum speed of the platform.

**Type**: Number
**Default**: 100.0
**Example**: max_speed(250.0)
**Unit**: m/s
"""
    
    # 初始化参数提取器
    extractor = ParameterExtractor()
    
    # 检查LLM状态
    if extractor.llm_client:
        print("✅ LLM客户端已初始化，将使用AI方法")
    else:
        print("⚠️  LLM客户端未初始化，将使用正则表达式方法")
    
    print("\n--- 提取平台参数 ---")
    
    # 提取参数
    parameters = extractor.extract_parameters(test_content, "platform")
    
    print(f"✅ 提取到 {len(parameters)} 个参数：\n")
    
    # 显示参数
    for i, param in enumerate(parameters, 1):
        print(f"{i}. {param['name']}")
        print(f"   类型: {param['type']}")
        print(f"   描述: {param['description']}")
        
        if param.get('default_value'):
            print(f"   默认值: {param['default_value']}")
        if param.get('unit'):
            print(f"   单位: {param['unit']}")
        if param.get('example'):
            print(f"   示例: {param['example']}")
        
        print(f"   提取方法: {param.get('extraction_method', 'unknown')}")
        print()

def test_different_parameter_types():
    """测试不同类型的参数提取"""
    
    print("=== 测试不同类型参数提取 ===\n")
    
    # 创建包含不同类型参数的内容
    test_content = """
# Mixed Parameters

## Platform Parameters
### platform_name()
Name of the platform.
Default: "unnamed"

## Sensor Parameters  
### sensor_range()
Detection range of the sensor.
Default: 10000.0
Unit: meters

## Weapon Parameters
### weapon_damage()
Damage value of the weapon.
Default: 100.0
"""
    
    extractor = ParameterExtractor()
    
    # 测试不同类型
    test_types = ["platform", "sensor", "weapon"]
    
    for param_type in test_types:
        print(f"--- 提取{param_type}参数 ---")
        
        parameters = extractor.extract_parameters(test_content, param_type)
        
        print(f"提取到 {len(parameters)} 个{param_type}参数")
        for param in parameters:
            print(f"  - {param['name']} ({param['type']})")
        print()

def test_regex_vs_llm():
    """对比正则表达式和LLM方法"""
    
    print("=== 正则表达式 vs LLM 对比 ===\n")
    
    test_content = """
# Test Parameters

### [position]() ...
Platform position parameter.

### platform.heading
Platform heading.

### `WsfPlatform`
Platform class.

### max_velocity()
Maximum velocity setting.
Type: Number
Default: 100.0
"""
    
    extractor = ParameterExtractor()
    
    # 强制使用正则表达式方法
    print("--- 正则表达式方法 ---")
    regex_params = extractor._extract_with_regex(test_content, "platform")
    print(f"提取到 {len(regex_params)} 个参数")
    for param in regex_params:
        print(f"  - {param['name']}")
    
    # 如果有LLM，测试LLM方法
    if extractor.llm_client:
        print("\n--- LLM方法 ---")
        llm_params = extractor._extract_with_llm(test_content, "platform")
        print(f"提取到 {len(llm_params)} 个参数")
        for param in llm_params:
            print(f"  - {param['name']}")
        
        # 对比结果
        regex_names = {p['name'] for p in regex_params}
        llm_names = {p['name'] for p in llm_params}
        
        print(f"\n--- 对比结果 ---")
        print(f"正则表达式独有: {regex_names - llm_names}")
        print(f"LLM独有: {llm_names - regex_names}")
        print(f"共同提取: {regex_names & llm_names}")
    else:
        print("\n⚠️  LLM客户端未初始化，跳过LLM方法测试")

def main():
    """主测试函数"""
    
    # 检查环境
    api_key = os.getenv('DEEPSEEK_API_KEY')
    if api_key:
        print("✅ 检测到DEEPSEEK_API_KEY")
    else:
        print("⚠️  未设置DEEPSEEK_API_KEY，将使用正则表达式方法")
    
    print("=" * 60)
    
    try:
        # 运行测试
        test_parameter_extractor_basic()
        test_different_parameter_types()
        test_regex_vs_llm()
        
        print("\n" + "=" * 60)
        print("🎉 测试完成！")
        print("\n核心功能验证:")
        print("✅ 参数提取器独立工作正常")
        print("✅ 支持不同类型的参数提取")
        print("✅ LLM和正则表达式方法都可用")
        print("✅ 按需提取架构验证成功")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
